<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5ba20455-fd28-4dcf-8535-256bf7775706" name="更改" comment="feat(master): ✨️添加安拆单位和维保单位的ID及名称字段">
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/java/org/dromara/projects/service/impl/PrjProjectsSyncServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/java/org/dromara/projects/service/impl/PrjProjectsSyncServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/java/org/dromara/system/service/impl/SysDeptServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-modules/ruoyi-system/src/main/java/org/dromara/system/service/impl/SysDeptServiceImpl.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="package-info" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="PREVIOUS_COMMIT_AUTHORS">
      <list>
        <option value="Redpanda" />
      </list>
    </option>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="MIXED" />
  </component>
  <component name="GradleLocalSettings">
    <option name="myGradleUserHome" value="$PROJECT_DIR$/../../../../Tool/RepositoryGradle" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\Tool\apache-maven-3.9.6" />
        <option name="localRepository" value="D:\Tool\Repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\Tool\apache-maven-3.9.6\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="explicitlyEnabledProfiles" value="dev" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2wOogbAP2XnW8KovX7yfweqVXAn" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;JUnit.AreaUtilsTest.testFormat.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.AreaUtilsTest.testGetArea.executor&quot;: &quot;Debug&quot;,
    &quot;JUnit.AreaUtilsTest.testGetAreaTree.executor&quot;: &quot;Run&quot;,
    &quot;Maven.municipal-safety-api [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.municipal-safety-api [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi-vue-plus [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi-vue-plus [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi-vue-plus [validate].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.Workspaces.CheckRemotesActivity&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.restore.workspace.module&quot;: &quot;true&quot;,
    &quot;Spring Boot.DromaraApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.MonitorAdminApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.MunicipalSafetyApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.SnailJobServerApplication.executor&quot;: &quot;Run&quot;,
    &quot;com.github.patou.gitmoji.display-icon&quot;: &quot;emoji&quot;,
    &quot;com.github.patou.gitmoji.include-gitmoji-description&quot;: &quot;true&quot;,
    &quot;com.github.patou.gitmoji.insert-in-cursor-position&quot;: &quot;true&quot;,
    &quot;com.github.patou.gitmoji.text-after-unicode&quot;: &quot; &quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/Work/job/municipal-safety/municipal-safety-api/ruoyi-admin/src/main&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.26666668&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.AiHazAnalysisTasksServiceImpl.executor&quot;: &quot;Debug&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ],
    &quot;com.intellij.ide.scratch.LRUPopupBuilder$1/SQL 方言&quot;: [
      &quot;MySQL&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\src\main" />
      <recent name="D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\src\main" />
      <recent name="D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-common\ruoyi-common-core\src\main\resources" />
      <recent name="D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-admin\src\main\resources" />
      <recent name="D:\Work\job\municipal-safety\municipal-safety-api\doc" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-modules\ruoyi-system\src\main\resources\mapper" />
      <recent name="D:\Work\job\municipal-safety\municipal-safety-api\ruoyi-common\ruoyi-common-core\src\main\resources" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="org.dromara.projects.domain.bo" />
      <recent name="org.dromara.projects.domain.vo" />
      <recent name="org.dromara.ai.enums" />
      <recent name="org.dromara.ai.controller" />
      <recent name="org.dromara.system.controller.system" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.MunicipalSafetyApplication">
    <configuration name="AiHazAnalysisTasksServiceImpl" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="org.dromara.ai.service.impl.AiHazAnalysisTasksServiceImpl" />
      <module name="ruoyi-system" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.dromara.ai.service.impl.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AreaUtilsTest.testFormat" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="ruoyi-admin" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.dromara.test.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="org.dromara.test" />
      <option name="MAIN_CLASS_NAME" value="org.dromara.test.AreaUtilsTest" />
      <option name="METHOD_NAME" value="testFormat" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AreaUtilsTest.testGetArea" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="ruoyi-admin" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.dromara.test.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="org.dromara.test" />
      <option name="MAIN_CLASS_NAME" value="org.dromara.test.AreaUtilsTest" />
      <option name="METHOD_NAME" value="testGetArea" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AreaUtilsTest.testGetAreaTree" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="ruoyi-admin" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.dromara.test.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="org.dromara.test" />
      <option name="MAIN_CLASS_NAME" value="org.dromara.test.AreaUtilsTest" />
      <option name="METHOD_NAME" value="testGetAreaTree" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JUnit" factoryName="JUnit">
      <shortenClasspath name="ARGS_FILE" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="municipal-safety-api" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="municipal-safety-api" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration name="MonitorAdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-monitor-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.monitor.admin.MonitorAdminApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MunicipalSafetyApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-admin" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.MunicipalSafetyApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SnailJobServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-snailjob-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.snailjob.SnailJobServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Docker.ruoyi-server" />
      <item itemvalue="Docker.ruoyi-monitor-admin" />
      <item itemvalue="Docker.ruoyi-snailjob-server" />
      <item itemvalue="JUnit.AreaUtilsTest.testFormat" />
      <item itemvalue="JUnit.AreaUtilsTest.testGetAreaTree" />
      <item itemvalue="JUnit.AreaUtilsTest.testGetArea" />
      <item itemvalue="Spring Boot.MonitorAdminApplication" />
      <item itemvalue="Spring Boot.MunicipalSafetyApplication" />
      <item itemvalue="Spring Boot.SnailJobServerApplication" />
      <item itemvalue="应用程序.AiHazAnalysisTasksServiceImpl" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.AreaUtilsTest.testGetArea" />
        <item itemvalue="JUnit.AreaUtilsTest.testFormat" />
        <item itemvalue="JUnit.AreaUtilsTest.testGetAreaTree" />
        <item itemvalue="应用程序.AiHazAnalysisTasksServiceImpl" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="5ba20455-fd28-4dcf-8535-256bf7775706" name="更改" comment="" />
      <created>1731552086285</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1731552086285</updated>
      <workItem from="1731552090134" duration="20000" />
      <workItem from="1745922094012" duration="2668000" />
      <workItem from="1745924820168" duration="2623000" />
      <workItem from="1745973063180" duration="7341000" />
      <workItem from="1745982800755" duration="420000" />
      <workItem from="1745984166665" duration="8639000" />
      <workItem from="1746058160535" duration="409000" />
      <workItem from="1746058713733" duration="1188000" />
      <workItem from="1746060391881" duration="11941000" />
      <workItem from="1746146097238" duration="9287000" />
      <workItem from="1746234563228" duration="1954000" />
      <workItem from="1746405616610" duration="3863000" />
      <workItem from="1746437459592" duration="608000" />
      <workItem from="1746492790873" duration="2171000" />
      <workItem from="1746523999964" duration="3288000" />
      <workItem from="1746578275516" duration="2313000" />
      <workItem from="1746583636259" duration="23375000" />
      <workItem from="1746667319527" duration="969000" />
      <workItem from="1746668315403" duration="19235000" />
      <workItem from="1746751536721" duration="293000" />
      <workItem from="1746751907576" duration="1237000" />
      <workItem from="1746753697524" duration="1663000" />
      <workItem from="1746756501700" duration="37000" />
      <workItem from="1746756552414" duration="140000" />
      <workItem from="1746756706140" duration="50328000" />
      <workItem from="1747009870487" duration="31686000" />
      <workItem from="1747096303391" duration="9519000" />
      <workItem from="1747121144053" duration="23080000" />
      <workItem from="1747202653755" duration="647000" />
      <workItem from="1747203439090" duration="16232000" />
      <workItem from="1747221491034" duration="17000" />
      <workItem from="1747225794100" duration="35000" />
      <workItem from="1747268720678" duration="42624000" />
      <workItem from="1747442920943" duration="394000" />
      <workItem from="1747443962546" duration="10423000" />
      <workItem from="1747615060317" duration="16911000" />
      <workItem from="1747702693059" duration="9547000" />
      <workItem from="1747722970490" duration="43611000" />
      <workItem from="1747814434169" duration="4611000" />
      <workItem from="1747838338224" duration="7746000" />
      <workItem from="1747960413125" duration="614000" />
      <workItem from="1747969942942" duration="3369000" />
      <workItem from="1747992712003" duration="606000" />
      <workItem from="1748227155519" duration="1250000" />
      <workItem from="1748314879599" duration="6000" />
      <workItem from="1748507322944" duration="58000" />
      <workItem from="1748511568986" duration="1955000" />
      <workItem from="1748520595329" duration="1658000" />
      <workItem from="1748933221029" duration="559000" />
      <workItem from="1749192022215" duration="3677000" />
      <workItem from="1749713129444" duration="2266000" />
      <workItem from="1750122888795" duration="714000" />
      <workItem from="1750317648125" duration="10249000" />
      <workItem from="1750380877555" duration="13026000" />
      <workItem from="1750469412621" duration="12201000" />
      <workItem from="1750557607936" duration="19092000" />
      <workItem from="1750678553080" duration="17000" />
      <workItem from="1750678669701" duration="3261000" />
      <workItem from="1750726601676" duration="1966000" />
      <workItem from="1750736307118" duration="2346000" />
      <workItem from="1750811476155" duration="4074000" />
      <workItem from="1750815915481" duration="266000" />
      <workItem from="1750816201100" duration="9243000" />
      <workItem from="1750903931528" duration="5000" />
      <workItem from="1750903948976" duration="1852000" />
      <workItem from="1750917766563" duration="1856000" />
      <workItem from="1750996221325" duration="1239000" />
      <workItem from="1751524236455" duration="1968000" />
      <workItem from="1751959507651" duration="10521000" />
      <workItem from="1752116481724" duration="2060000" />
      <workItem from="1753685258702" duration="36000" />
      <workItem from="1753690325549" duration="28000" />
      <workItem from="1753691371404" duration="7391000" />
      <workItem from="1753705828401" duration="41000" />
      <workItem from="1753750140123" duration="7106000" />
      <workItem from="1753770432003" duration="13252000" />
      <workItem from="1753785990772" duration="3576000" />
      <workItem from="1753836158340" duration="6000" />
      <workItem from="1753836184748" duration="1248000" />
      <workItem from="1753837644663" duration="17134000" />
      <workItem from="1753862198827" duration="11332000" />
      <workItem from="1753878592725" duration="66000" />
      <workItem from="1753922540915" duration="2348000" />
    </task>
    <task id="LOCAL-00052" summary="feat: ✨️ 更新生产环境配置">
      <option name="closed" value="true" />
      <created>1747452010819</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1747452010820</updated>
    </task>
    <task id="LOCAL-00053" summary="feat: ✨️ 优化AI分析结果处理逻辑，增强参数校验及结果插入功能">
      <option name="closed" value="true" />
      <created>1747616603284</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1747616603284</updated>
    </task>
    <task id="LOCAL-00054" summary="feat: ✨️ 修改隐患ID字段名称为itemId">
      <option name="closed" value="true" />
      <created>1747619225644</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1747619225644</updated>
    </task>
    <task id="LOCAL-00055" summary="feat: ✨️ 修改隐患ID字段名称为itemId">
      <option name="closed" value="true" />
      <created>1747619462101</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1747619462101</updated>
    </task>
    <task id="LOCAL-00056" summary="feat: ✨️ 修正隐患ID字段名称为ItemId">
      <option name="closed" value="true" />
      <created>1747621820346</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1747621820346</updated>
    </task>
    <task id="LOCAL-00057" summary="feat: ✨️ 添加企业信息列表为空时的处理逻辑">
      <option name="closed" value="true" />
      <created>1747628024736</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1747628024736</updated>
    </task>
    <task id="LOCAL-00058" summary="feat: ✨️ 添加sourceType字段以区分预警来源类型">
      <option name="closed" value="true" />
      <created>1747705779898</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1747705779898</updated>
    </task>
    <task id="LOCAL-00059" summary="refactor: remove unnecessary permission check from project list endpoint">
      <option name="closed" value="true" />
      <created>1747707777966</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1747707777966</updated>
    </task>
    <task id="LOCAL-00060" summary="feat: ✨️ set sourceType to &quot;CAMERA&quot; in AiHazAnalysisTasksBo">
      <option name="closed" value="true" />
      <created>1747709843511</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1747709843511</updated>
    </task>
    <task id="LOCAL-00061" summary="feat(master): ✨️修改上传文件大小限制">
      <option name="closed" value="true" />
      <created>1747723024521</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1747723024521</updated>
    </task>
    <task id="LOCAL-00062" summary="feat(master): ✨️手动添加逻辑删除条件">
      <option name="closed" value="true" />
      <created>1747725223194</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1747725223194</updated>
    </task>
    <task id="LOCAL-00063" summary="feat(master): ✨️添加复检状态字段">
      <option name="closed" value="true" />
      <created>1747728773204</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1747728773204</updated>
    </task>
    <task id="LOCAL-00064" summary="feat(master): ✨️更新任务查询逻辑，添加工程名称字段">
      <option name="closed" value="true" />
      <created>1747739321443</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1747739321443</updated>
    </task>
    <task id="LOCAL-00065" summary="feat(master): ✨️添加翻译注解以支持照片文档ID的URL转换">
      <option name="closed" value="true" />
      <created>1747741446426</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1747741446426</updated>
    </task>
    <task id="LOCAL-00066" summary="feat(master): ✨️更新照片文档ID字段，添加URL转换支持">
      <option name="closed" value="true" />
      <created>1747743483100</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1747743483100</updated>
    </task>
    <task id="LOCAL-00067" summary="feat(master): ✨️启用监控中心和SnailJob配置，更新数据库连接信息">
      <option name="closed" value="true" />
      <created>1747745955990</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1747745955990</updated>
    </task>
    <task id="LOCAL-00068" summary="feat(master): ✨️更新项目最终名称为ms-monitor-admin和ms-snailjob-server">
      <option name="closed" value="true" />
      <created>1747746919389</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1747746919389</updated>
    </task>
    <task id="LOCAL-00069" summary="feat(master): ✨️更新日志路径为ms-monitor-admin和ms-snailjob-server">
      <option name="closed" value="true" />
      <created>1747749051145</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>1747749051145</updated>
    </task>
    <task id="LOCAL-00070" summary="feat(master): ✨️更新日志路径为/data/ms/api/logs/ms-monitor-admin和/data/ms/api/logs/ms-snailjob-server">
      <option name="closed" value="true" />
      <created>1747750057158</created>
      <option name="number" value="00070" />
      <option name="presentableId" value="LOCAL-00070" />
      <option name="project" value="LOCAL" />
      <updated>1747750057158</updated>
    </task>
    <task id="LOCAL-00071" summary="feat(master): ✨️111">
      <option name="closed" value="true" />
      <created>1747753221288</created>
      <option name="number" value="00071" />
      <option name="presentableId" value="LOCAL-00071" />
      <option name="project" value="LOCAL" />
      <updated>1747753221288</updated>
    </task>
    <task id="LOCAL-00072" summary="feat(master): ✨️222">
      <option name="closed" value="true" />
      <created>1747753427109</created>
      <option name="number" value="00072" />
      <option name="presentableId" value="LOCAL-00072" />
      <option name="project" value="LOCAL" />
      <updated>1747753427110</updated>
    </task>
    <task id="LOCAL-00073" summary="feat(master): ✨️ 添加逻辑删除条件">
      <option name="closed" value="true" />
      <created>1747789008408</created>
      <option name="number" value="00073" />
      <option name="presentableId" value="LOCAL-00073" />
      <option name="project" value="LOCAL" />
      <updated>1747789008408</updated>
    </task>
    <task id="LOCAL-00074" summary="feat(master): ✨️ 添加复检状态字段及相关逻辑">
      <option name="closed" value="true" />
      <created>1747791124669</created>
      <option name="number" value="00074" />
      <option name="presentableId" value="LOCAL-00074" />
      <option name="project" value="LOCAL" />
      <updated>1747791124669</updated>
    </task>
    <task id="LOCAL-00075" summary="feat(master): ✨️ 更新日志文件路径及服务监控中心标题">
      <option name="closed" value="true" />
      <created>1747797568713</created>
      <option name="number" value="00075" />
      <option name="presentableId" value="LOCAL-00075" />
      <option name="project" value="LOCAL" />
      <updated>1747797568713</updated>
    </task>
    <task id="LOCAL-00076" summary="feat(master): ✨️ 更新服务监控中心URL为生产环境地址">
      <option name="closed" value="true" />
      <created>1747798312608</created>
      <option name="number" value="00076" />
      <option name="presentableId" value="LOCAL-00076" />
      <option name="project" value="LOCAL" />
      <updated>1747798312608</updated>
    </task>
    <task id="LOCAL-00077" summary="feat(master): ✨️ 更新服务监控中心和客户端地址为生产环境IP">
      <option name="closed" value="true" />
      <created>1747801056511</created>
      <option name="number" value="00077" />
      <option name="presentableId" value="LOCAL-00077" />
      <option name="project" value="LOCAL" />
      <updated>1747801056511</updated>
    </task>
    <task id="LOCAL-00078" summary="feat(master): ✨️ 更新服务监控中心公共URL配置">
      <option name="closed" value="true" />
      <created>1747805135056</created>
      <option name="number" value="00078" />
      <option name="presentableId" value="LOCAL-00078" />
      <option name="project" value="LOCAL" />
      <updated>1747805135056</updated>
    </task>
    <task id="LOCAL-00079" summary="feat(master): ✨️ 更新监控中心用户名为jtszkj">
      <option name="closed" value="true" />
      <created>1747819355688</created>
      <option name="number" value="00079" />
      <option name="presentableId" value="LOCAL-00079" />
      <option name="project" value="LOCAL" />
      <updated>1747819355688</updated>
    </task>
    <task id="LOCAL-00080" summary="feat(master): ✨️ 移除服务监控中心公共URL配置">
      <option name="closed" value="true" />
      <created>1747819374194</created>
      <option name="number" value="00080" />
      <option name="presentableId" value="LOCAL-00080" />
      <option name="project" value="LOCAL" />
      <updated>1747819374194</updated>
    </task>
    <task id="LOCAL-00081" summary="feat(master): ✨️ 更新登录欢迎信息为工程建设领域全生命周期安全隐患管控系统">
      <option name="closed" value="true" />
      <created>1747844682850</created>
      <option name="number" value="00081" />
      <option name="presentableId" value="LOCAL-00081" />
      <option name="project" value="LOCAL" />
      <updated>1747844682850</updated>
    </task>
    <task id="LOCAL-00082" summary="fix: 🐛 优化查询排序，移除项目ID和项ID排序">
      <option name="closed" value="true" />
      <created>1748520697115</created>
      <option name="number" value="00082" />
      <option name="presentableId" value="LOCAL-00082" />
      <option name="project" value="LOCAL" />
      <updated>1748520697116</updated>
    </task>
    <task id="LOCAL-00083" summary="feat(master): ✨️添加政府和施工方部门及以下数据权限支持">
      <option name="closed" value="true" />
      <created>1750410287175</created>
      <option name="number" value="00083" />
      <option name="presentableId" value="LOCAL-00083" />
      <option name="project" value="LOCAL" />
      <updated>1750410287175</updated>
    </task>
    <task id="LOCAL-00084" summary="feat(master): ✨️ 更新项目列表数据权限字段名称">
      <option name="closed" value="true" />
      <created>1750489372726</created>
      <option name="number" value="00084" />
      <option name="presentableId" value="LOCAL-00084" />
      <option name="project" value="LOCAL" />
      <updated>1750489372727</updated>
    </task>
    <task id="LOCAL-00085" summary="feat(master): ✨️ 更新项目列表数据权限字段名称为deptNameZf">
      <option name="closed" value="true" />
      <created>1750563110001</created>
      <option name="number" value="00085" />
      <option name="presentableId" value="LOCAL-00085" />
      <option name="project" value="LOCAL" />
      <updated>1750563110001</updated>
    </task>
    <task id="LOCAL-00086" summary="feat(master): ✨️添加专家项目权限支持，优化数据权限查询">
      <option name="closed" value="true" />
      <created>1750580023002</created>
      <option name="number" value="00086" />
      <option name="presentableId" value="LOCAL-00086" />
      <option name="project" value="LOCAL" />
      <updated>1750580023002</updated>
    </task>
    <task id="LOCAL-00087" summary="feat(master): ✨️添加专家项目权限支持，优化数据权限查询">
      <option name="closed" value="true" />
      <created>1750580050624</created>
      <option name="number" value="00087" />
      <option name="presentableId" value="LOCAL-00087" />
      <option name="project" value="LOCAL" />
      <updated>1750580050624</updated>
    </task>
    <task id="LOCAL-00088" summary="feat(master): ✨️移除缓存注解以优化专家项目获取逻辑">
      <option name="closed" value="true" />
      <created>1750582125476</created>
      <option name="number" value="00088" />
      <option name="presentableId" value="LOCAL-00088" />
      <option name="project" value="LOCAL" />
      <updated>1750582125479</updated>
    </task>
    <task id="LOCAL-00089" summary="feat(master): ✨️添加监理数据权限">
      <option name="closed" value="true" />
      <created>1750856439374</created>
      <option name="number" value="00089" />
      <option name="presentableId" value="LOCAL-00089" />
      <option name="project" value="LOCAL" />
      <updated>1750856439374</updated>
    </task>
    <task id="LOCAL-00090" summary="feat(master): ✨️日志">
      <option name="closed" value="true" />
      <created>1751524488918</created>
      <option name="number" value="00090" />
      <option name="presentableId" value="LOCAL-00090" />
      <option name="project" value="LOCAL" />
      <updated>1751524488918</updated>
    </task>
    <task id="LOCAL-00091" summary="feat(master): ✨️更新AI分析任务状态，添加新状态和响应码处理">
      <option name="closed" value="true" />
      <created>1751970795860</created>
      <option name="number" value="00091" />
      <option name="presentableId" value="LOCAL-00091" />
      <option name="project" value="LOCAL" />
      <updated>1751970795860</updated>
    </task>
    <task id="LOCAL-00092" summary="feat(master): ✨临时取消限流">
      <option name="closed" value="true" />
      <created>1752116563975</created>
      <option name="number" value="00092" />
      <option name="presentableId" value="LOCAL-00092" />
      <option name="project" value="LOCAL" />
      <updated>1752116563975</updated>
    </task>
    <task id="LOCAL-00093" summary="feat(master): ✨11">
      <option name="closed" value="true" />
      <created>1752116598889</created>
      <option name="number" value="00093" />
      <option name="presentableId" value="LOCAL-00093" />
      <option name="project" value="LOCAL" />
      <updated>1752116598889</updated>
    </task>
    <task id="LOCAL-00094" summary="feat(master): ✨️项目同步功能">
      <option name="closed" value="true" />
      <created>1753695009802</created>
      <option name="number" value="00094" />
      <option name="presentableId" value="LOCAL-00094" />
      <option name="project" value="LOCAL" />
      <updated>1753695009802</updated>
    </task>
    <task id="LOCAL-00095" summary="feat(master): ✨️添加施工许可证号查询与数据验证">
      <option name="closed" value="true" />
      <created>1753751733884</created>
      <option name="number" value="00095" />
      <option name="presentableId" value="LOCAL-00095" />
      <option name="project" value="LOCAL" />
      <updated>1753751733885</updated>
    </task>
    <task id="LOCAL-00096" summary="feat(master): ✨️支持多企业类型的角色分配与部门信息管理">
      <option name="closed" value="true" />
      <created>1753755196041</created>
      <option name="number" value="00096" />
      <option name="presentableId" value="LOCAL-00096" />
      <option name="project" value="LOCAL" />
      <updated>1753755196041</updated>
    </task>
    <task id="LOCAL-00097" summary="feat(master): ✨️根据地区名称获取编码">
      <option name="closed" value="true" />
      <created>1753774476130</created>
      <option name="number" value="00097" />
      <option name="presentableId" value="LOCAL-00097" />
      <option name="project" value="LOCAL" />
      <updated>1753774476130</updated>
    </task>
    <task id="LOCAL-00098" summary="feat(master): ✨️项目人员同步功能">
      <option name="closed" value="true" />
      <created>1753778529567</created>
      <option name="number" value="00098" />
      <option name="presentableId" value="LOCAL-00098" />
      <option name="project" value="LOCAL" />
      <updated>1753778529567</updated>
    </task>
    <task id="LOCAL-00099" summary="feat(master): ✨️增加人员项目绑定，添加null检查">
      <option name="closed" value="true" />
      <created>1753838541963</created>
      <option name="number" value="00099" />
      <option name="presentableId" value="LOCAL-00099" />
      <option name="project" value="LOCAL" />
      <updated>1753838541963</updated>
    </task>
    <task id="LOCAL-00100" summary="feat(master): ✨️添加安拆单位和维保单位的ID及名称字段">
      <option name="closed" value="true" />
      <created>1753845026784</created>
      <option name="number" value="00100" />
      <option name="presentableId" value="LOCAL-00100" />
      <option name="project" value="LOCAL" />
      <updated>1753845026784</updated>
    </task>
    <option name="localTasksCounter" value="101" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="feat(master): ✨️ 更新监控中心用户名为jtszkj" />
    <MESSAGE value="feat(master): ✨️ 移除服务监控中心公共URL配置" />
    <MESSAGE value="feat(master): ✨️ 更新登录欢迎信息为工程建设领域全生命周期安全隐患管控系统" />
    <MESSAGE value="fix:  优化查询排序，移除项目ID和项ID排序" />
    <MESSAGE value="feat: ✨️" />
    <MESSAGE value="feat(master): ✨️添加政府和施工方部门及以下数据权限支持" />
    <MESSAGE value="feat(master): ✨️ 更新项目列表数据权限字段名称" />
    <MESSAGE value="feat(master): ✨️ 更新项目列表数据权限字段名称为deptNameZf" />
    <MESSAGE value="feat: ✨️ 添加专家项目权限支持，优化数据权限查询" />
    <MESSAGE value="feat(master): ✨️添加专家项目权限支持，优化数据权限查询" />
    <MESSAGE value="refactor(master):  移除缓存注解以优化专家项目获取逻辑" />
    <MESSAGE value="feat(master): ✨️移除缓存注解以优化专家项目获取逻辑" />
    <MESSAGE value="feat(master): ✨️添加监理数据权限" />
    <MESSAGE value="feat(master): ✨️日志" />
    <MESSAGE value="feat(master): ✨️更新AI分析任务状态，添加新状态和响应码处理" />
    <MESSAGE value="feat(master): ✨临时取消限流" />
    <MESSAGE value="feat(master): ✨11" />
    <MESSAGE value="feat(sync): add project synchronization service and DTOs for external API integration" />
    <MESSAGE value="feat(master): ✨️项目同步功能" />
    <MESSAGE value="feat(master): ✨️添加施工许可证号查询与数据验证" />
    <MESSAGE value="feat(master): ✨️支持多企业类型的角色分配与部门信息管理" />
    <MESSAGE value="feat(master): ✨️根据地区名称获取编码" />
    <MESSAGE value="feat(master): ✨️项目人员同步功能" />
    <MESSAGE value="feat(master): ✨️增加人员项目绑定，添加null检查" />
    <MESSAGE value="feat(master): ✨️添加安拆单位和维保单位的ID及名称字段" />
    <option name="LAST_COMMIT_MESSAGE" value="feat(master): ✨️添加安拆单位和维保单位的ID及名称字段" />
    <option name="OPTIMIZE_IMPORTS_BEFORE_PROJECT_COMMIT" value="true" />
    <option name="REFORMAT_BEFORE_PROJECT_COMMIT" value="true" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>